@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");

/* Animation for placeholder text - matches reference more closely */
@keyframes placeholderAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
    visibility: hidden;
  }
  1%,
  5% {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
  6%,
  100% {
    opacity: 0;
    transform: translateY(-20px);
    visibility: hidden;
  }
}

/* Animasi panah bergerak */
@keyframes arrowMove {
  0% { transform: translateX(0); }
  50% { transform: translateX(3px); }
  100% { transform: translateX(0); }
}

/* CSS untuk tombol floating arrow */
.floating-arrow {
  position: absolute;
  right: 5px;
  top: 45%;
  transform: translateY(-50%);
  height: 36px;
  background-color: rgba(255, 245, 240, 0.95);
  border: 1px solid #FFDFD1;
  border-radius: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  padding: 0 15px 0 12px;
  cursor: pointer;
  z-index: 10;
  transition: opacity 0.8s ease, transform 0.8s ease;
  opacity: 1;
}

.floating-arrow.hidden {
  opacity: 0;
  transform: translateY(-50%) translateX(10px);
  pointer-events: none;
}

.floating-arrow:active {
  transform: translateY(-50%) scale(0.97);
}

.floating-arrow-text {
  font-size: 12px;
  font-weight: 600;
  color: #FF5722;
  margin-right: 6px;
}

.floating-arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-arrow-icon svg {
  width: 15px;
  height: 15px;
  color: #FF5722;
  animation: arrowMove 1.5s infinite ease-in-out;
}

/* Kategori Styles */
.categories-container {
  display: flex;
  overflow-x: auto;
  padding-bottom: 10px;
  gap: 12px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  padding: 8px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  cursor: pointer;
}

.category-item:hover {
  transform: translateY(-2px);
}

.category-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-name {
  font-size: 12px;
  text-align: center;
  color: #333;
  line-height: 1.2;
}

/* Desktop Categories Grid */
.desktop-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  grid-template-rows: repeat(2, auto);
  grid-auto-flow: row;
  gap: 12px;
  padding: 4px 16px 12px 4px;
  width: 100%;
}

/* Gradient untuk efek fade di sisi kanan */
.categories-container-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));
  pointer-events: none;
}

/* CSS untuk expanded categories */
.expanded-categories {
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease;
}

.expanded-categories.active {
  height: auto !important;
  overflow: visible;
}

.expanded-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  padding: 5px 10px 10px 10px;
}

@media (min-width: 768px) {
  .expanded-grid {
    grid-template-columns: repeat(7, 1fr);
    gap: 15px;
  }
}

.placeholder-animate {
  animation: placeholderAnimation 45s ease-in-out infinite;
}

/* Search input styling */
.search-input::placeholder {
  color: #666;
  opacity: 1;
}

.search-input:focus::placeholder {
  opacity: 0.7;
}

/* Cart and chat icon styling */
.cart-icon,
.chat-icon {
  position: relative;
  color: transparent;
  font-size: 22px;
  cursor: pointer;
}

/* For webkit browsers to create outline effect on icons */
@supports (-webkit-text-stroke: 1px white) {
  .cart-icon {
    -webkit-text-stroke: 1.3px white;
  }

  .chat-icon {
    -webkit-text-stroke: 2px white;
  }
}

/* For non-webkit browsers fallback */
@supports not (-webkit-text-stroke: 1px white) {
  .cart-icon,
  .chat-icon {
    color: white;
    text-shadow: 0 0 1px white;
  }
}
