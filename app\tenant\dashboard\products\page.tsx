"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { useProducts, type Product } from "@/hooks/use-products"
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Package,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Star,
  TrendingUp,
  ShoppingCart
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"


const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge variant="default" className="bg-green-100 text-green-800">Aktif</Badge>
    case "draft":
      return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Draft</Badge>
    case "inactive":
      return <Badge variant="outline">Tidak Aktif</Badge>
    case "archived":
      return <Badge variant="secondary" className="bg-red-100 text-red-800">Archived</Badge>
    default:
      return <Badge variant="outline">Unknown</Badge>
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(amount)
}

export default function ProductsPage() {
  const {
    products,
    loading,
    fetchProducts,
    createProduct,
    updateProductStatus,
    toggleFeatured,
    deleteProduct,
    getProductStats
  } = useProducts()

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [stats, setStats] = useState<any>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState({
    sku: "",
    name: "",
    slug: "",
    description: "",
    price: 0,
    category_id: "",
    brand_id: "",
    status: "draft" as const
  })

  // Load stats on mount
  useEffect(() => {
    const loadStats = async () => {
      const productStats = await getProductStats()
      setStats(productStats)
    }
    loadStats()
  }, [getProductStats])

  // Apply filters
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (product.category?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (product.brand?.name || '').toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || product.status === statusFilter
    const matchesCategory = categoryFilter === "all" || product.category?.id === categoryFilter

    return matchesSearch && matchesStatus && matchesCategory
  })

  // Get unique categories for filter
  const categories = products
    .map(p => p.category)
    .filter(Boolean)
    .reduce((unique, category) => {
      if (!unique.find(c => c!.id === category!.id)) {
        unique.push(category)
      }
      return unique
    }, [] as typeof products[0]['category'][])

  const handleStatusChange = async (productId: string, newStatus: Product['status']) => {
    await updateProductStatus(productId, newStatus)
    // Refresh stats
    const productStats = await getProductStats()
    setStats(productStats)
  }

  const handleToggleFeatured = async (productId: string) => {
    await toggleFeatured(productId)
  }

  const handleDelete = async (productId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus produk ini?')) {
      await deleteProduct(productId)
      // Refresh stats
      const productStats = await getProductStats()
      setStats(productStats)
    }
  }

  const handleCreateProduct = async () => {
    // Validasi form
    if (!formData.name.trim()) {
      alert('Nama produk harus diisi')
      return
    }
    if (!formData.sku.trim()) {
      alert('SKU harus diisi')
      return
    }
    if (formData.price <= 0) {
      alert('Harga harus lebih dari 0')
      return
    }

    setIsCreating(true)
    try {
      const success = await createProduct(formData)
      if (success) {
        setShowCreateDialog(false)
        setFormData({
          sku: "",
          name: "",
          slug: "",
          description: "",
          price: 0,
          category_id: "",
          brand_id: "",
          status: "draft" as const
        })
        // Refresh stats
        const productStats = await getProductStats()
        setStats(productStats)
      }
    } finally {
      setIsCreating(false)
    }
  }

  const handleNameChange = (name: string) => {
    const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    setFormData(prev => ({ ...prev, name, slug }))
  }

  const handleViewDetail = (product: Product) => {
    setSelectedProduct(product)
    setShowDetailDialog(true)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Kelola Produk</h1>
            <p className="text-muted-foreground">
              Manage dan moderasi semua produk dalam marketplace
            </p>
          </div>
        </div>

        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Tambah Produk Baru
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Produk</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.total}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.active} aktif
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.draft}</div>
                <p className="text-xs text-muted-foreground">
                  Belum dipublikasi
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stok Habis</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.out_of_stock}</div>
                <p className="text-xs text-muted-foreground">
                  Perlu restock
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Featured</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{stats.featured}</div>
                <p className="text-xs text-muted-foreground">
                  Produk unggulan
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Daftar Produk</CardTitle>
              <CardDescription>
                Manage dan moderasi semua produk dalam marketplace
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari produk..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-[250px]"
                />
              </div>

              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kategori</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category!.id} value={category!.id}>
                      {category!.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="inactive">Tidak Aktif</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[200px]" />
                    <Skeleton className="h-4 w-[100px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Produk</TableHead>
                    <TableHead>Brand</TableHead>
                    <TableHead>Kategori</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Stok</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Featured</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="relative h-12 w-12 rounded-md overflow-hidden bg-gray-100">
                            {product.featured_image ? (
                              <Image
                                src={product.featured_image}
                                alt={product.name}
                                fill
                                className="object-cover"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full">
                                <Package className="h-6 w-6 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{product.name}</div>
                            <div className="text-sm text-muted-foreground">SKU: {product.sku}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {product.brand ? (
                          <Badge variant="outline">{product.brand.name}</Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {product.category ? (
                          <Badge variant="outline">{product.category.name}</Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            Rp {product.price.toLocaleString('id-ID')}
                          </div>
                          {product.compare_price && product.compare_price > product.price && (
                            <div className="text-sm text-muted-foreground line-through">
                              Rp {product.compare_price.toLocaleString('id-ID')}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={product.inventory_quantity === 0 ? "text-red-600 font-medium" :
                                       product.inventory_quantity <= product.low_stock_threshold ? "text-orange-600 font-medium" : ""}>
                          {product.inventory_quantity}
                        </span>
                      </TableCell>
                      <TableCell>{getStatusBadge(product.status)}</TableCell>
                      <TableCell>
                        {product.featured ? (
                          <Badge variant="default" className="bg-yellow-100 text-yellow-800">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetail(product)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Produk
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleFeatured(product.id)}>
                              <Star className="h-4 w-4 mr-2" />
                              {product.featured ? 'Remove Featured' : 'Set Featured'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleStatusChange(product.id, product.status === 'active' ? 'inactive' : 'active')}>
                              {product.status === 'active' ? (
                                <>
                                  <XCircle className="h-4 w-4 mr-2" />
                                  Nonaktifkan
                                </>
                              ) : (
                                <>
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Aktifkan
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDelete(product.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Hapus Produk
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {!loading && filteredProducts.length === 0 && (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Tidak ada produk ditemukan</h3>
                  <p className="text-muted-foreground">
                    {searchTerm || statusFilter !== "all" || categoryFilter !== "all"
                      ? "Coba ubah filter atau kata kunci pencarian"
                      : "Belum ada produk yang ditambahkan"
                    }
                  </p>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Create Product Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Tambah Produk Baru</DialogTitle>
            <DialogDescription>
              Buat produk baru untuk marketplace Anda.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nama Produk</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Masukkan nama produk"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  value={formData.sku}
                  onChange={(e) => setFormData(prev => ({ ...prev, sku: e.target.value }))}
                  placeholder="Masukkan SKU produk"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                placeholder="slug-produk"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Deskripsi</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Deskripsi produk"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="price">Harga</Label>
                <Input
                  id="price"
                  type="number"
                  value={formData.price}
                  onChange={(e) => setFormData(prev => ({ ...prev, price: Number(e.target.value) }))}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value: any) => setFormData(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Tidak Aktif</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Batal
            </Button>
            <Button onClick={handleCreateProduct} disabled={isCreating}>
              {isCreating ? "Menyimpan..." : "Simpan Produk"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Product Detail Dialog */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detail Produk</DialogTitle>
            <DialogDescription>
              Informasi lengkap tentang produk
            </DialogDescription>
          </DialogHeader>
          {selectedProduct && (
            <div className="grid gap-6 py-4">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2">Informasi Dasar</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Nama:</strong> {selectedProduct.name}</div>
                    <div><strong>SKU:</strong> {selectedProduct.sku}</div>
                    <div><strong>Slug:</strong> {selectedProduct.slug}</div>
                    <div><strong>Status:</strong> {selectedProduct.status}</div>
                    <div><strong>Harga:</strong> Rp {selectedProduct.price.toLocaleString('id-ID')}</div>
                    {selectedProduct.compare_price && (
                      <div><strong>Harga Coret:</strong> Rp {selectedProduct.compare_price.toLocaleString('id-ID')}</div>
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Kategori & Brand</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Kategori:</strong> {selectedProduct.category?.name || '-'}</div>
                    <div><strong>Brand:</strong> {selectedProduct.brand?.name || '-'}</div>
                    <div><strong>Featured:</strong> {selectedProduct.featured ? 'Ya' : 'Tidak'}</div>
                    <div><strong>Visibility:</strong> {selectedProduct.visibility}</div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Deskripsi</h3>
                <p className="text-sm text-gray-600">{selectedProduct.description || 'Tidak ada deskripsi'}</p>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2">Inventory</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Stok:</strong> {selectedProduct.inventory_quantity}</div>
                    <div><strong>Track Inventory:</strong> {selectedProduct.track_inventory ? 'Ya' : 'Tidak'}</div>
                    <div><strong>Low Stock Threshold:</strong> {selectedProduct.low_stock_threshold}</div>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Shipping & Tax</h3>
                  <div className="space-y-2 text-sm">
                    <div><strong>Requires Shipping:</strong> {selectedProduct.requires_shipping ? 'Ya' : 'Tidak'}</div>
                    <div><strong>Tax Status:</strong> {selectedProduct.tax_status}</div>
                    <div><strong>Weight:</strong> {selectedProduct.weight || '-'} kg</div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Tanggal</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div><strong>Dibuat:</strong> {new Date(selectedProduct.created_at).toLocaleDateString('id-ID')}</div>
                  <div><strong>Diupdate:</strong> {new Date(selectedProduct.updated_at).toLocaleDateString('id-ID')}</div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDetailDialog(false)}>
              Tutup
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}