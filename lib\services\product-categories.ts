import { getClient } from '@/lib/supabase'

export interface ProductCategory {
  id: string
  name: string
  slug: string
  description?: string
  parent_id?: string
  tenant_id: string
  image_url?: string
  icon?: string
  color: string
  sort_order: number
  is_active: boolean
  meta_title?: string
  meta_description?: string
  meta_keywords: string[]
  created_at: string
  updated_at: string
  // Relations
  parent?: ProductCategory
  children?: ProductCategory[]
  product_count?: number
}

export interface ProductCategoryFilters {
  search?: string
  parent_id?: string
  is_active?: boolean
}

export interface ProductCategoryCreate {
  name: string
  slug: string
  description?: string
  parent_id?: string
  tenant_id?: string
  image_url?: string
  icon?: string
  color?: string
  sort_order?: number
  is_active?: boolean
  meta_title?: string
  meta_description?: string
  meta_keywords?: string[]
}

export interface ProductCategoryUpdate extends Partial<ProductCategoryCreate> {}

class ProductCategoryService {
  private supabase = getClient()
  private readonly DEFAULT_TENANT_ID = '00000000-0000-0000-0000-000000000001'

  // Get all categories with optional filters
  async getCategories(filters?: ProductCategoryFilters): Promise<ProductCategory[]> {
    let query = this.supabase
      .from('product_categories')
      .select(`
        *,
        parent:product_categories!parent_id(id, name, slug),
        children:product_categories!parent_id(id, name, slug, sort_order)
      `)
      .eq('tenant_id', this.DEFAULT_TENANT_ID)
      .order('sort_order', { ascending: true })

    // Apply filters
    if (filters?.search) {
      const searchTerm = `%${filters.search}%`
      query = query.or(`name.ilike.${searchTerm},description.ilike.${searchTerm},slug.ilike.${searchTerm}`)
    }

    if (filters?.parent_id !== undefined) {
      if (filters.parent_id === null || filters.parent_id === '') {
        query = query.is('parent_id', null)
      } else {
        query = query.eq('parent_id', filters.parent_id)
      }
    }

    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`)
    }

    return data || []
  }

  // Get categories in tree structure
  async getCategoriesTree(): Promise<ProductCategory[]> {
    const categories = await this.getCategories()
    
    // Build tree structure
    const categoryMap = new Map<string, ProductCategory>()
    const rootCategories: ProductCategory[] = []

    // First pass: create map and initialize children arrays
    categories.forEach(category => {
      category.children = []
      categoryMap.set(category.id, category)
    })

    // Second pass: build tree
    categories.forEach(category => {
      if (category.parent_id) {
        const parent = categoryMap.get(category.parent_id)
        if (parent) {
          parent.children!.push(category)
        }
      } else {
        rootCategories.push(category)
      }
    })

    return rootCategories
  }

  // Get single category by ID
  async getCategory(id: string): Promise<ProductCategory | null> {
    const { data, error } = await this.supabase
      .from('product_categories')
      .select(`
        *,
        parent:product_categories!parent_id(id, name, slug),
        children:product_categories!parent_id(id, name, slug, sort_order)
      `)
      .eq('id', id)
      .eq('tenant_id', this.DEFAULT_TENANT_ID)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch category: ${error.message}`)
    }

    return data
  }

  // Get category by slug
  async getCategoryBySlug(slug: string): Promise<ProductCategory | null> {
    const { data, error } = await this.supabase
      .from('product_categories')
      .select(`
        *,
        parent:product_categories!parent_id(id, name, slug),
        children:product_categories!parent_id(id, name, slug, sort_order)
      `)
      .eq('slug', slug)
      .eq('tenant_id', this.DEFAULT_TENANT_ID)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch category: ${error.message}`)
    }

    return data
  }

  // Create new category
  async createCategory(category: ProductCategoryCreate): Promise<ProductCategory> {
    // Check if slug already exists
    const existingCategory = await this.getCategoryBySlug(category.slug)
    if (existingCategory) {
      throw new Error('Slug already exists')
    }

    const { data, error } = await this.supabase
      .from('product_categories')
      .insert([{
        ...category,
        tenant_id: category.tenant_id || this.DEFAULT_TENANT_ID,
        color: category.color || '#4F46E5',
        is_active: category.is_active !== undefined ? category.is_active : true,
        sort_order: category.sort_order || 0,
        meta_keywords: category.meta_keywords || []
      }])
      .select(`
        *,
        parent:product_categories!parent_id(id, name, slug),
        children:product_categories!parent_id(id, name, slug, sort_order)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`)
    }

    return data
  }

  // Update category
  async updateCategory(id: string, updates: ProductCategoryUpdate): Promise<ProductCategory> {
    // If slug is being updated, check if it already exists
    if (updates.slug) {
      const existingCategory = await this.getCategoryBySlug(updates.slug)
      if (existingCategory && existingCategory.id !== id) {
        throw new Error('Slug already exists')
      }
    }

    // Prevent circular parent relationships
    if (updates.parent_id) {
      const isCircular = await this.checkCircularParent(id, updates.parent_id)
      if (isCircular) {
        throw new Error('Cannot set parent: would create circular relationship')
      }
    }

    const { data, error } = await this.supabase
      .from('product_categories')
      .update(updates)
      .eq('id', id)
      .eq('tenant_id', this.DEFAULT_TENANT_ID)
      .select(`
        *,
        parent:product_categories!parent_id(id, name, slug),
        children:product_categories!parent_id(id, name, slug, sort_order)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update category: ${error.message}`)
    }

    return data
  }

  // Delete category
  async deleteCategory(id: string): Promise<void> {
    // Check if category has children
    const children = await this.getCategories({ parent_id: id })
    if (children.length > 0) {
      throw new Error('Cannot delete category with subcategories')
    }

    // Check if category has products
    const { count, error: countError } = await this.supabase
      .from('products')
      .select('id', { count: 'exact' })
      .eq('category_id', id)

    if (countError) {
      throw new Error(`Failed to check products: ${countError.message}`)
    }

    if (count && count > 0) {
      throw new Error('Cannot delete category with products')
    }

    const { error } = await this.supabase
      .from('product_categories')
      .delete()
      .eq('id', id)
      .eq('tenant_id', this.DEFAULT_TENANT_ID)

    if (error) {
      throw new Error(`Failed to delete category: ${error.message}`)
    }
  }

  // Toggle category active status
  async toggleCategoryStatus(id: string): Promise<ProductCategory> {
    const category = await this.getCategory(id)
    if (!category) {
      throw new Error('Category not found')
    }

    return this.updateCategory(id, { is_active: !category.is_active })
  }

  // Reorder categories
  async reorderCategories(categoryOrders: { id: string; sort_order: number }[]): Promise<void> {
    const updates = categoryOrders.map(({ id, sort_order }) => 
      this.supabase
        .from('product_categories')
        .update({ sort_order })
        .eq('id', id)
    )

    const results = await Promise.all(updates)
    
    for (const result of results) {
      if (result.error) {
        throw new Error(`Failed to reorder categories: ${result.error.message}`)
      }
    }
  }

  // Check for circular parent relationship
  private async checkCircularParent(categoryId: string, parentId: string): Promise<boolean> {
    if (categoryId === parentId) {
      return true
    }

    const parent = await this.getCategory(parentId)
    if (!parent || !parent.parent_id) {
      return false
    }

    return this.checkCircularParent(categoryId, parent.parent_id)
  }

  // Get category statistics
  async getCategoryStats(): Promise<{
    total: number
    active: number
    inactive: number
    with_products: number
    root_categories: number
  }> {
    const { data, error } = await this.supabase
      .from('product_categories')
      .select('is_active, parent_id')
      .eq('tenant_id', this.DEFAULT_TENANT_ID)

    if (error) {
      throw new Error(`Failed to fetch category stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      active: 0,
      inactive: 0,
      with_products: 0,
      root_categories: 0,
    }

    data.forEach((category) => {
      if (category.is_active) {
        stats.active++
      } else {
        stats.inactive++
      }

      if (!category.parent_id) {
        stats.root_categories++
      }
    })

    // Get categories with products count
    const { data: categoriesWithProducts, error: productsError } = await this.supabase
      .from('products')
      .select('category_id')
      .not('category_id', 'is', null)

    if (!productsError && categoriesWithProducts) {
      const uniqueCategories = new Set(categoriesWithProducts.map(p => p.category_id))
      stats.with_products = uniqueCategories.size
    }

    return stats
  }

  // Get categories with product counts
  async getCategoriesWithProductCounts(): Promise<ProductCategory[]> {
    const categories = await this.getCategories()
    
    // Get product counts for each category
    const { data: productCounts, error } = await this.supabase
      .from('products')
      .select('category_id')
      .not('category_id', 'is', null)

    if (error) {
      throw new Error(`Failed to fetch product counts: ${error.message}`)
    }

    // Count products per category
    const countMap = new Map<string, number>()
    productCounts?.forEach(product => {
      const count = countMap.get(product.category_id) || 0
      countMap.set(product.category_id, count + 1)
    })

    // Add product counts to categories
    return categories.map(category => ({
      ...category,
      product_count: countMap.get(category.id) || 0
    }))
  }
}

export const productCategoryService = new ProductCategoryService()
