import { getServerClient } from '@/lib/supabase'

export interface ImportExportJob {
  id: string
  type: 'import' | 'export'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  file_name?: string
  file_url?: string
  file_size?: number
  total_records: number
  processed_records: number
  successful_records: number
  failed_records: number
  error_log: string[]
  mapping_config: Record<string, any>
  filters: Record<string, any>
  options: Record<string, any>
  started_at?: string
  completed_at?: string
  created_by?: string
  created_at: string
  updated_at: string
  // Additional fields for development
  progress?: number
  processed_count?: number
  success_count?: number
  error_count?: number
  result_summary?: Record<string, any>
}

export interface ImportExportJobFilters {
  type?: 'import' | 'export'
  status?: string
  created_by?: string
  date_from?: string
  date_to?: string
}

export interface ImportExportJobCreate {
  type: 'import' | 'export'
  file_name?: string
  file_url?: string
  file_size?: number
  mapping_config?: Record<string, any>
  filters?: Record<string, any>
  options?: Record<string, any>
  created_by?: string
}

export interface ImportExportJobUpdate {
  status?: ImportExportJob['status']
  total_records?: number
  processed_records?: number
  successful_records?: number
  failed_records?: number
  error_log?: string[]
  started_at?: string
  completed_at?: string
  progress?: number
  processed_count?: number
  success_count?: number
  error_count?: number
  result_summary?: Record<string, any>
  file_url?: string
  file_size?: number
}

// In-memory storage for development - using global to persist across requests
declare global {
  var __importExportJobs: ImportExportJob[] | undefined
  var __nextJobId: number | undefined
}

// Initialize global storage
if (!global.__importExportJobs) {
  global.__importExportJobs = []
}

if (!global.__nextJobId) {
  global.__nextJobId = 1
}

// Helper function to generate ID
function generateJobId(): string {
  const id = `job-${global.__nextJobId}`
  global.__nextJobId = (global.__nextJobId || 1) + 1
  return id
}

// Helper function to get jobs
function getJobs(): ImportExportJob[] {
  return global.__importExportJobs || []
}

// Helper function to set jobs
function setJobs(jobs: ImportExportJob[]): void {
  global.__importExportJobs = jobs
}

class ProductImportExportService {
  private supabase = getServerClient() // Use server client for bypassing RLS
  private useInMemory = false // Now using Supabase for both development and production

  // Get all jobs with optional filters
  async getJobs(filters?: ImportExportJobFilters): Promise<ImportExportJob[]> {
    if (this.useInMemory) {
      let result = [...getJobs()]

      // Apply filters
      if (filters?.type) {
        result = result.filter((job: ImportExportJob) => job.type === filters.type)
      }

      if (filters?.status) {
        result = result.filter((job: ImportExportJob) => job.status === filters.status)
      }

      if (filters?.created_by) {
        result = result.filter((job: ImportExportJob) => job.created_by === filters.created_by)
      }

      if (filters?.date_from) {
        result = result.filter((job: ImportExportJob) => job.created_at >= filters.date_from!)
      }

      if (filters?.date_to) {
        result = result.filter((job: ImportExportJob) => job.created_at <= filters.date_to!)
      }

      // Sort by created_at descending
      result.sort((a: ImportExportJob, b: ImportExportJob) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

      return result
    }

    let query = this.supabase
      .from('product_import_export_jobs')
      .select('*')
      .order('created_at', { ascending: false })

    // Apply filters
    if (filters?.type) {
      query = query.eq('type', filters.type)
    }

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    if (filters?.created_by) {
      query = query.eq('created_by', filters.created_by)
    }

    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from)
    }

    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch jobs: ${error.message}`)
    }

    return (data || []) as unknown as ImportExportJob[]
  }

  // Get single job by ID
  async getJob(id: string): Promise<ImportExportJob | null> {
    if (this.useInMemory) {
      return getJobs().find((job: ImportExportJob) => job.id === id) || null
    }

    const { data, error } = await this.supabase
      .from('product_import_export_jobs')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch job: ${error.message}`)
    }

    return data as unknown as ImportExportJob
  }

  // Create new job
  async createJob(job: ImportExportJobCreate): Promise<ImportExportJob> {
    if (this.useInMemory) {
      const newJob: ImportExportJob = {
        id: generateJobId(),
        type: job.type,
        status: 'pending',
        file_name: job.file_name,
        file_url: job.file_url,
        file_size: job.file_size,
        total_records: 0,
        processed_records: 0,
        successful_records: 0,
        failed_records: 0,
        error_log: [],
        mapping_config: job.mapping_config || {},
        filters: job.filters || {},
        options: job.options || {},
        created_by: job.created_by,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        progress: 0
      }

      const currentJobs = getJobs()
      currentJobs.push(newJob)
      setJobs(currentJobs)
      return newJob
    }

    const { data, error } = await this.supabase
      .from('product_import_export_jobs')
      .insert([{
        ...job,
        status: 'pending',
        total_records: 0,
        processed_records: 0,
        successful_records: 0,
        failed_records: 0,
        error_log: [],
        mapping_config: job.mapping_config || {},
        filters: job.filters || {},
        options: job.options || {}
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create job: ${error.message}`)
    }

    return data as unknown as ImportExportJob
  }

  // Update job
  async updateJob(id: string, updates: ImportExportJobUpdate): Promise<ImportExportJob> {
    if (this.useInMemory) {
      const currentJobs = getJobs()
      const index = currentJobs.findIndex((job: ImportExportJob) => job.id === id)
      if (index === -1) {
        throw new Error('Job not found')
      }

      const updateData = { ...updates, updated_at: new Date().toISOString() }
      currentJobs[index] = { ...currentJobs[index], ...updateData }
      setJobs(currentJobs)
      return currentJobs[index]
    }

    const { data, error } = await this.supabase
      .from('product_import_export_jobs')
      .update(updates as any)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update job: ${error.message}`)
    }

    return data as unknown as ImportExportJob
  }

  // Delete job
  async deleteJob(id: string): Promise<void> {
    if (this.useInMemory) {
      const currentJobs = getJobs()
      const index = currentJobs.findIndex((job: ImportExportJob) => job.id === id)
      if (index === -1) {
        throw new Error('Job not found')
      }
      currentJobs.splice(index, 1)
      setJobs(currentJobs)
      return
    }

    const { error } = await this.supabase
      .from('product_import_export_jobs')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete job: ${error.message}`)
    }
  }

  // Update job status and progress
  async updateJobStatus(id: string, status: ImportExportJob['status'], progress?: number): Promise<ImportExportJob> {
    const updates: ImportExportJobUpdate = { status }

    if (progress !== undefined) {
      updates.progress = progress
    }

    if (status === 'processing' && !updates.started_at) {
      updates.started_at = new Date().toISOString()
    }

    if (['completed', 'failed', 'cancelled'].includes(status)) {
      updates.completed_at = new Date().toISOString()
    }

    return this.updateJob(id, updates)
  }

  // Retry job
  async retryJob(id: string): Promise<ImportExportJob> {
    return this.updateJob(id, {
      status: 'pending',
      progress: 0,
      processed_records: 0,
      successful_records: 0,
      failed_records: 0,
      error_log: [],
      started_at: undefined,
      completed_at: undefined
    })
  }

  // Start job processing
  async startJob(id: string): Promise<ImportExportJob> {
    return this.updateJob(id, {
      status: 'processing',
      started_at: new Date().toISOString()
    })
  }

  // Complete job
  async completeJob(id: string): Promise<ImportExportJob> {
    return this.updateJob(id, {
      status: 'completed',
      completed_at: new Date().toISOString()
    })
  }

  // Fail job
  async failJob(id: string, errorMessage: string): Promise<ImportExportJob> {
    const job = await this.getJob(id)
    if (!job) {
      throw new Error('Job not found')
    }

    const errorLog = [...job.error_log, errorMessage]

    return this.updateJob(id, {
      status: 'failed',
      error_log: errorLog,
      completed_at: new Date().toISOString()
    })
  }

  // Cancel job
  async cancelJob(id: string): Promise<ImportExportJob> {
    return this.updateJob(id, {
      status: 'cancelled',
      completed_at: new Date().toISOString()
    })
  }

  // Update job progress
  async updateJobProgress(
    id: string,
    processed: number,
    successful: number,
    failed: number,
    errors?: string[]
  ): Promise<ImportExportJob> {
    const updates: ImportExportJobUpdate = {
      processed_records: processed,
      successful_records: successful,
      failed_records: failed
    }

    if (errors && errors.length > 0) {
      const job = await this.getJob(id)
      if (job) {
        updates.error_log = [...job.error_log, ...errors]
      }
    }

    return this.updateJob(id, updates)
  }

  // Get job statistics
  async getJobStats(): Promise<{
    total: number
    pending: number
    processing: number
    completed: number
    failed: number
    cancelled: number
    imports: number
    exports: number
  }> {
    if (this.useInMemory) {
      const currentJobs = getJobs()
      const stats = {
        total: currentJobs.length,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        cancelled: 0,
        imports: 0,
        exports: 0,
      }

      currentJobs.forEach((job: ImportExportJob) => {
        // Status counts
        switch (job.status) {
          case 'pending':
            stats.pending++
            break
          case 'processing':
            stats.processing++
            break
          case 'completed':
            stats.completed++
            break
          case 'failed':
            stats.failed++
            break
          case 'cancelled':
            stats.cancelled++
            break
        }

        // Type counts
        if (job.type === 'import') {
          stats.imports++
        } else {
          stats.exports++
        }
      })

      return stats
    }

    const { data, error } = await this.supabase
      .from('product_import_export_jobs')
      .select('type, status')

    if (error) {
      throw new Error(`Failed to fetch job stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
      imports: 0,
      exports: 0,
    }

    data.forEach((job) => {
      // Status counts
      switch (job.status) {
        case 'pending':
          stats.pending++
          break
        case 'processing':
          stats.processing++
          break
        case 'completed':
          stats.completed++
          break
        case 'failed':
          stats.failed++
          break
        case 'cancelled':
          stats.cancelled++
          break
      }

      // Type counts
      if (job.type === 'import') {
        stats.imports++
      } else {
        stats.exports++
      }
    })

    return stats
  }

  // Get recent jobs
  async getRecentJobs(limit = 10): Promise<ImportExportJob[]> {
    const { data, error } = await this.supabase
      .from('product_import_export_jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      throw new Error(`Failed to fetch recent jobs: ${error.message}`)
    }

    return (data || []) as unknown as ImportExportJob[]
  }

  // Get active jobs (pending or processing)
  async getActiveJobs(): Promise<ImportExportJob[]> {
    const { data, error } = await this.supabase
      .from('product_import_export_jobs')
      .select('*')
      .in('status', ['pending', 'processing'])
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch active jobs: ${error.message}`)
    }

    return (data || []) as unknown as ImportExportJob[]
  }

  // Cleanup old completed jobs
  async cleanupOldJobs(daysOld = 30): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)

    const { data, error } = await this.supabase
      .from('product_import_export_jobs')
      .delete()
      .in('status', ['completed', 'failed', 'cancelled'])
      .lt('completed_at', cutoffDate.toISOString())
      .select('id')

    if (error) {
      throw new Error(`Failed to cleanup old jobs: ${error.message}`)
    }

    return data?.length || 0
  }

  // Generate export job for products
  async createExportJob(filters: Record<string, any> = {}, options: Record<string, any> = {}): Promise<ImportExportJob> {
    const fileName = `products_export_${new Date().toISOString().split('T')[0]}.csv`

    return this.createJob({
      type: 'export',
      file_name: fileName,
      filters,
      options: {
        format: 'csv',
        include_images: true,
        include_variants: true,
        ...options
      }
    })
  }

  // Create import job for products
  async createImportJob(
    fileName: string,
    fileUrl: string,
    fileSize: number,
    mappingConfig: Record<string, any> = {},
    options: Record<string, any> = {}
  ): Promise<ImportExportJob> {
    return this.createJob({
      type: 'import',
      file_name: fileName,
      file_url: fileUrl,
      file_size: fileSize,
      mapping_config: mappingConfig,
      options: {
        skip_duplicates: true,
        update_existing: false,
        validate_data: true,
        ...options
      }
    })
  }
}

export const productImportExportService = new ProductImportExportService()
