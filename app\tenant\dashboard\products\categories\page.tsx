"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import {
  ArrowLeft,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Package,
  Grid3X3,
  TrendingUp,
  Settings,
  MoreVertical,
  Filter,
  Tag
} from "lucide-react"
import Link from "next/link"

import { useProductCategories, type ProductCategory } from "@/hooks/use-product-categories"

function getStatusBadge(isActive: boolean) {
  return isActive ? (
    <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>
  ) : (
    <Badge variant="secondary" className="bg-gray-100 text-gray-800">Inactive</Badge>
  )
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export default function ProductCategoriesPage() {
  const {
    categories,
    loading,
    toggleCategoryStatus,
    deleteCategory,
    getCategoryStats
  } = useProductCategories()

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [stats, setStats] = useState<any>(null)

  // Load stats on mount
  useEffect(() => {
    const loadStats = async () => {
      const categoryStats = await getCategoryStats()
      setStats(categoryStats)
    }
    loadStats()
  }, [getCategoryStats])

  // Apply filters
  const filteredCategories = categories.filter(cat => {
    const matchesSearch = cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (cat.description || '').toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" ||
                         (statusFilter === "active" && cat.is_active) ||
                         (statusFilter === "inactive" && !cat.is_active)
    return matchesSearch && matchesStatus
  })

  const handleToggleStatus = async (categoryId: string) => {
    await toggleCategoryStatus(categoryId)
    // Refresh stats
    const categoryStats = await getCategoryStats()
    setStats(categoryStats)
  }

  const handleDelete = async (categoryId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus kategori ini?')) {
      await deleteCategory(categoryId)
      // Refresh stats
      const categoryStats = await getCategoryStats()
      setStats(categoryStats)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/tenant/dashboard/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Product Categories</h1>
            <p className="text-muted-foreground">
              Kelola kategori produk dan sub-kategori
            </p>
          </div>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Tambah Kategori
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Kategori</CardTitle>
            <Grid3X3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{stats.total}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Kategori Aktif</CardTitle>
            <Grid3X3 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dengan Produk</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-blue-600">{stats.with_products}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Root Categories</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            {loading || !stats ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-orange-600">{stats.root_categories}</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cari kategori produk..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Semua Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </CardHeader>
      </Card>

      {/* Categories List */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="flex items-start gap-6">
                  <Skeleton className="w-20 h-20 rounded-lg" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-6 w-1/3" />
                    <Skeleton className="h-4 w-2/3" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredCategories.map((category) => (
            <Card key={category.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start gap-6">
                  {/* Category Image */}
                  <div className="flex-shrink-0">
                    <div
                      className="w-20 h-20 rounded-lg flex items-center justify-center text-white font-bold text-lg"
                      style={{ backgroundColor: category.color }}
                    >
                      {category.icon ? (
                        <span>{category.icon}</span>
                      ) : (
                        category.name.charAt(0).toUpperCase()
                      )}
                    </div>
                  </div>

                  {/* Category Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold">{category.name}</h3>
                          {getStatusBadge(category.is_active)}
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{category.description}</p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>Slug: /{category.slug}</span>
                          <span>Sort: {category.sort_order}</span>
                          {category.parent && <span>Parent: {category.parent.name}</span>}
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-muted-foreground">Total Produk</p>
                      <p className="text-lg font-bold text-blue-600">{category.product_count || 0}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Sub-kategori</p>
                      <p className="text-lg font-bold text-purple-600">{category.children?.length || 0}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Sort Order</p>
                      <p className="text-lg font-bold text-orange-600">{category.sort_order}</p>
                    </div>
                  </div>

                  {/* Children Categories */}
                  {category.children && category.children.length > 0 && (
                    <div className="mb-4">
                      <p className="text-xs text-muted-foreground mb-2">Sub-kategori</p>
                      <div className="flex flex-wrap gap-1">
                        {category.children.map((child) => (
                          <Badge key={child.id} variant="outline" className="text-xs">
                            {child.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* SEO Info */}
                  {(category.meta_title || category.meta_description) && (
                    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                      {category.meta_title && (
                        <>
                          <p className="text-xs text-muted-foreground mb-1">SEO Title</p>
                          <p className="text-sm font-medium">{category.meta_title}</p>
                        </>
                      )}
                      {category.meta_description && (
                        <>
                          <p className="text-xs text-muted-foreground mb-1 mt-2">SEO Description</p>
                          <p className="text-xs text-gray-600">{category.meta_description}</p>
                        </>
                      )}
                    </div>
                  )}

                  {/* Dates & Actions */}
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground">
                      <p>Dibuat: {formatDate(category.created_at)}</p>
                      <p>Update: {formatDate(category.updated_at)}</p>
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleToggleStatus(category.id)}
                      >
                        {category.is_active ? 'Nonaktifkan' : 'Aktifkan'}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDelete(category.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Hapus
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        </div>
      )}

      {!loading && filteredCategories.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Grid3X3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Tidak ada kategori ditemukan</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || statusFilter !== "all"
                ? "Coba ubah filter atau kata kunci pencarian"
                : "Belum ada kategori produk yang ditambahkan"
              }
            </p>
            {!searchTerm && statusFilter === "all" && (
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Kategori Pertama
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
