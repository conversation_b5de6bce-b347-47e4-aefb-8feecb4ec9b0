import { getServerClient } from '@/lib/supabase'

export interface ProductModeration {
  id: string
  product_id: string
  status: 'pending' | 'approved' | 'rejected' | 'under_review' | 'requires_changes'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  reviewer_id?: string
  review_notes?: string
  rejection_reason?: string
  required_changes: string[]
  flags: string[]
  auto_flags: string[]
  review_checklist: Record<string, boolean>
  submitted_at: string
  reviewed_at?: string
  approved_at?: string
  rejected_at?: string
  created_at: string
  updated_at: string
  // Relations
  product?: {
    id: string
    name: string
    sku: string
    price: number
    featured_image?: string
    status: string
  }
  reviewer?: {
    id: string
    name: string
    email: string
  }
}

export interface ProductModerationFilters {
  status?: string
  priority?: string
  reviewer_id?: string
  product_id?: string
  date_from?: string
  date_to?: string
  has_flags?: boolean
}

export interface ProductModerationCreate {
  product_id: string
  priority?: ProductModeration['priority']
  flags?: string[]
  auto_flags?: string[]
  review_checklist?: Record<string, boolean>
}

export interface ProductModerationUpdate {
  status?: ProductModeration['status']
  priority?: ProductModeration['priority']
  reviewer_id?: string
  review_notes?: string
  rejection_reason?: string
  required_changes?: string[]
  flags?: string[]
  review_checklist?: Record<string, boolean>
}

// In-memory storage for development - using global to persist across requests
declare global {
  var __productModerations: ProductModeration[] | undefined
  var __nextModerationId: number | undefined
}

// Initialize global storage
if (!global.__productModerations) {
  global.__productModerations = []
}

if (!global.__nextModerationId) {
  global.__nextModerationId = 1
}

// Helper function to generate ID
function generateId(): string {
  const id = `moderation-${global.__nextModerationId}`
  global.__nextModerationId = (global.__nextModerationId || 1) + 1
  return id
}

// Helper function to get moderations
function getModerations(): ProductModeration[] {
  return global.__productModerations || []
}

// Helper function to set moderations
function setModerations(moderations: ProductModeration[]): void {
  global.__productModerations = moderations
}

class ProductModerationService {
  private supabase = getServerClient() // Use server client for bypassing RLS
  private useInMemory = false // Now using Supabase for both development and production

  // Get all moderation entries with optional filters
  async getModerations(filters?: ProductModerationFilters): Promise<ProductModeration[]> {
    if (this.useInMemory) {
      let result = [...getModerations()]

      // Apply filters
      if (filters?.status) {
        result = result.filter((m: ProductModeration) => m.status === filters.status)
      }

      if (filters?.priority) {
        result = result.filter((m: ProductModeration) => m.priority === filters.priority)
      }

      if (filters?.reviewer_id) {
        result = result.filter((m: ProductModeration) => m.reviewer_id === filters.reviewer_id)
      }

      if (filters?.product_id) {
        result = result.filter((m: ProductModeration) => m.product_id === filters.product_id)
      }

      if (filters?.date_from) {
        result = result.filter((m: ProductModeration) => m.submitted_at >= filters.date_from!)
      }

      if (filters?.date_to) {
        result = result.filter((m: ProductModeration) => m.submitted_at <= filters.date_to!)
      }

      if (filters?.has_flags) {
        result = result.filter((m: ProductModeration) => m.flags && m.flags.length > 0)
      }

      // Sort by submitted_at descending
      result.sort((a: ProductModeration, b: ProductModeration) => new Date(b.submitted_at).getTime() - new Date(a.submitted_at).getTime())

      return result
    }

    // Supabase implementation - for now return empty array since table doesn't exist
    // TODO: Implement when product_moderation table is created
    return []
  }

  // Get single moderation by ID
  async getModeration(id: string): Promise<ProductModeration | null> {
    if (this.useInMemory) {
      return getModerations().find((m: ProductModeration) => m.id === id) || null
    }

    // TODO: Implement Supabase when table exists
    return null
  }

  // Get moderation by product ID
  async getModerationByProduct(productId: string): Promise<ProductModeration | null> {
    if (this.useInMemory) {
      const found = getModerations()
        .filter((m: ProductModeration) => m.product_id === productId)
        .sort((a: ProductModeration, b: ProductModeration) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      return found[0] || null
    }

    // TODO: Implement Supabase when table exists
    return null
  }

  // Create new moderation entry
  async createModeration(moderation: ProductModerationCreate | any): Promise<ProductModeration> {
    if (this.useInMemory) {
      const newModeration: ProductModeration = {
        id: generateId(),
        product_id: moderation.product_id,
        status: moderation.status || 'pending',
        priority: moderation.priority || 'normal',
        reviewer_id: moderation.reviewer_id,
        review_notes: moderation.review_notes,
        rejection_reason: moderation.rejection_reason,
        required_changes: moderation.required_changes || [],
        flags: moderation.flags || [],
        auto_flags: moderation.auto_flags || [],
        review_checklist: moderation.review_checklist || {},
        submitted_at: new Date().toISOString(),
        reviewed_at: moderation.reviewed_at,
        approved_at: moderation.approved_at,
        rejected_at: moderation.rejected_at,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        product: moderation.product,
        reviewer: moderation.reviewer
      }

      const currentModerations = getModerations()
      currentModerations.push(newModeration)
      setModerations(currentModerations)
      return newModeration
    }

    // TODO: Implement Supabase when table exists
    throw new Error('Supabase implementation not available yet')
  }

  // Update moderation
  async updateModeration(id: string, updates: ProductModerationUpdate): Promise<ProductModeration> {
    if (this.useInMemory) {
      const currentModerations = getModerations()
      const index = currentModerations.findIndex((m: ProductModeration) => m.id === id)
      if (index === -1) {
        throw new Error('Moderation not found')
      }

      const updateData: any = { ...updates }

      // Set timestamps based on status
      if (updates.status === 'approved') {
        updateData.approved_at = new Date().toISOString()
        updateData.reviewed_at = new Date().toISOString()
      } else if (updates.status === 'rejected') {
        updateData.rejected_at = new Date().toISOString()
        updateData.reviewed_at = new Date().toISOString()
      } else if (updates.status === 'under_review') {
        updateData.reviewed_at = new Date().toISOString()
      }

      updateData.updated_at = new Date().toISOString()

      currentModerations[index] = { ...currentModerations[index], ...updateData }
      setModerations(currentModerations)
      return currentModerations[index]
    }

    // TODO: Implement Supabase when table exists
    throw new Error('Supabase implementation not available yet')
  }

  // Delete moderation
  async deleteModeration(id: string): Promise<void> {
    if (this.useInMemory) {
      const currentModerations = getModerations()
      const index = currentModerations.findIndex((m: ProductModeration) => m.id === id)
      if (index === -1) {
        throw new Error('Moderation not found')
      }
      currentModerations.splice(index, 1)
      setModerations(currentModerations)
      return
    }

    // TODO: Implement Supabase when table exists
    throw new Error('Supabase implementation not available yet')
  }

  // Approve product
  async approveProduct(id: string, reviewerId: string, notes?: string): Promise<ProductModeration> {
    const moderation = await this.getModeration(id)
    if (!moderation) {
      throw new Error('Moderation not found')
    }

    // For in-memory, we don't need to update actual product table
    // In real implementation, this would update the product status

    return this.updateModeration(id, {
      status: 'approved',
      reviewer_id: reviewerId,
      review_notes: notes
    })
  }

  // Reject product
  async rejectProduct(id: string, reviewerId: string, reason: string, requiredChanges?: string[]): Promise<ProductModeration> {
    const moderation = await this.getModeration(id)
    if (!moderation) {
      throw new Error('Moderation not found')
    }

    // For in-memory, we don't need to update actual product table
    // In real implementation, this would update the product status

    return this.updateModeration(id, {
      status: 'rejected',
      reviewer_id: reviewerId,
      rejection_reason: reason,
      required_changes: requiredChanges || []
    })
  }

  // Request changes
  async requestChanges(id: string, reviewerId: string, requiredChanges: string[], notes?: string): Promise<ProductModeration> {
    return this.updateModeration(id, {
      status: 'requires_changes',
      reviewer_id: reviewerId,
      required_changes: requiredChanges,
      review_notes: notes
    })
  }

  // Set under review
  async setUnderReview(id: string, reviewerId: string, notes?: string): Promise<ProductModeration> {
    return this.updateModeration(id, {
      status: 'under_review',
      reviewer_id: reviewerId,
      review_notes: notes
    })
  }

  // Add flags to moderation
  async addFlags(id: string, flags: string[]): Promise<ProductModeration> {
    const moderation = await this.getModeration(id)
    if (!moderation) {
      throw new Error('Moderation not found')
    }

    const existingFlags = moderation.flags || []
    const newFlags = [...new Set([...existingFlags, ...flags])]

    return this.updateModeration(id, { flags: newFlags })
  }

  // Remove flags from moderation
  async removeFlags(id: string, flagsToRemove: string[]): Promise<ProductModeration> {
    const moderation = await this.getModeration(id)
    if (!moderation) {
      throw new Error('Moderation not found')
    }

    const existingFlags = moderation.flags || []
    const newFlags = existingFlags.filter(flag => !flagsToRemove.includes(flag))

    return this.updateModeration(id, { flags: newFlags })
  }

  // Update review checklist
  async updateChecklist(id: string, checklist: Record<string, boolean>): Promise<ProductModeration> {
    const moderation = await this.getModeration(id)
    if (!moderation) {
      throw new Error('Moderation not found')
    }

    const updatedChecklist = { ...moderation.review_checklist, ...checklist }

    return this.updateModeration(id, { review_checklist: updatedChecklist })
  }

  // Get moderation statistics
  async getModerationStats(): Promise<{
    total: number
    pending: number
    under_review: number
    approved: number
    rejected: number
    requires_changes: number
    flagged: number
    high_priority: number
  }> {
    if (this.useInMemory) {
      const currentModerations = getModerations()
      const stats = {
        total: currentModerations.length,
        pending: 0,
        under_review: 0,
        approved: 0,
        rejected: 0,
        requires_changes: 0,
        flagged: 0,
        high_priority: 0,
      }

      currentModerations.forEach((moderation: ProductModeration) => {
        // Status counts
        switch (moderation.status) {
          case 'pending':
            stats.pending++
            break
          case 'under_review':
            stats.under_review++
            break
          case 'approved':
            stats.approved++
            break
          case 'rejected':
            stats.rejected++
            break
          case 'requires_changes':
            stats.requires_changes++
            break
        }

        // Priority counts
        if (moderation.priority === 'high' || moderation.priority === 'urgent') {
          stats.high_priority++
        }

        // Flagged count
        if (moderation.flags && Array.isArray(moderation.flags) && moderation.flags.length > 0) {
          stats.flagged++
        }
      })

      return stats
    }

    // TODO: Implement Supabase when table exists
    return {
      total: 0,
      pending: 0,
      under_review: 0,
      approved: 0,
      rejected: 0,
      requires_changes: 0,
      flagged: 0,
      high_priority: 0,
    }
  }

  // Get pending moderations for review queue
  async getPendingModerations(_limit = 50): Promise<ProductModeration[]> {
    return this.getModerations({
      status: 'pending'
    })
  }

  // Get moderations assigned to reviewer
  async getReviewerModerations(reviewerId: string): Promise<ProductModeration[]> {
    return this.getModerations({
      reviewer_id: reviewerId
    })
  }

  // Bulk approve products
  async bulkApprove(ids: string[], reviewerId: string, notes?: string): Promise<ProductModeration[]> {
    const results: ProductModeration[] = []

    for (const id of ids) {
      try {
        const result = await this.approveProduct(id, reviewerId, notes)
        results.push(result)
      } catch (error) {
        console.error(`Failed to approve moderation ${id}:`, error)
      }
    }

    return results
  }

  // Bulk reject products
  async bulkReject(ids: string[], reviewerId: string, reason: string): Promise<ProductModeration[]> {
    const results: ProductModeration[] = []

    for (const id of ids) {
      try {
        const result = await this.rejectProduct(id, reviewerId, reason)
        results.push(result)
      } catch (error) {
        console.error(`Failed to reject moderation ${id}:`, error)
      }
    }

    return results
  }
}

export const productModerationService = new ProductModerationService()
