import { getClient } from '@/lib/supabase'

export interface Product {
  id: string
  sku: string
  name: string
  slug: string
  description?: string
  short_description?: string
  price: number
  compare_price?: number
  cost_price?: number
  track_inventory: boolean
  inventory_quantity: number
  low_stock_threshold: number
  weight?: number
  dimensions?: {
    length?: number
    width?: number
    height?: number
  }
  category_id?: string
  brand_id?: string
  tags: string[]
  images: string[]
  featured_image?: string
  gallery: string[]
  variants: any[]
  attributes: Record<string, any>
  seo_title?: string
  seo_description?: string
  seo_keywords: string[]
  status: 'draft' | 'active' | 'inactive' | 'archived'
  visibility: 'visible' | 'hidden' | 'catalog_only'
  featured: boolean
  digital: boolean
  downloadable: boolean
  download_files: string[]
  requires_shipping: boolean
  tax_status: 'taxable' | 'none'
  tax_class: string
  meta_data: Record<string, any>
  created_by?: string
  updated_by?: string
  published_at?: string
  created_at: string
  updated_at: string
  // Relations
  category?: {
    id: string
    name: string
    slug: string
  }
  brand?: {
    id: string
    name: string
    slug: string
  }
}

export interface ProductFilters {
  search?: string
  category_id?: string
  brand_id?: string
  status?: string
  visibility?: string
  featured?: boolean
  min_price?: number
  max_price?: number
  tags?: string[]
  in_stock?: boolean
}

export interface ProductCreate {
  sku: string
  name: string
  slug: string
  description?: string
  short_description?: string
  price: number
  compare_price?: number
  cost_price?: number
  track_inventory?: boolean
  inventory_quantity?: number
  low_stock_threshold?: number
  weight?: number
  dimensions?: object
  category_id?: string
  brand_id?: string
  tags?: string[]
  images?: string[]
  featured_image?: string
  gallery?: string[]
  variants?: any[]
  attributes?: object
  seo_title?: string
  seo_description?: string
  seo_keywords?: string[]
  status?: Product['status']
  visibility?: Product['visibility']
  featured?: boolean
  digital?: boolean
  downloadable?: boolean
  download_files?: string[]
  requires_shipping?: boolean
  tax_status?: Product['tax_status']
  tax_class?: string
  meta_data?: object
}

export interface ProductUpdate extends Partial<ProductCreate> {}

class ProductService {
  private supabase = getClient()

  // Get all products with optional filters and relations
  async getProducts(filters?: ProductFilters): Promise<Product[]> {
    try {
      console.log('Mengambil daftar produk dengan filter:', filters);
      
      let query = this.supabase
        .from('products')
        .select('*')

      // Apply filters
      if (filters?.search) {
        query = query.ilike('name', `%${filters.search}%`)
      }
      
      if (filters?.category_id) {
        query = query.eq('category_id', filters.category_id)
      }
      
      if (filters?.brand_id) {
        query = query.eq('brand_id', filters.brand_id)
      }
      
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }
      
      if (filters?.visibility) {
        query = query.eq('visibility', filters.visibility)
      }
      
      if (filters?.featured !== undefined) {
        query = query.eq('featured', filters.featured)
      }
      
      if (filters?.min_price !== undefined) {
        query = query.gte('price', filters.min_price)
      }
      
      if (filters?.max_price !== undefined) {
        query = query.lte('price', filters.max_price)
      }
      
      if (filters?.tags?.length) {
        query = query.contains('tags', filters.tags)
      }
      
      if (filters?.in_stock !== undefined) {
        if (filters.in_stock) {
          query = query.gt('inventory_quantity', 0)
        } else {
          query = query.or('inventory_quantity.eq.0,inventory_quantity.is.null')
        }
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error saat mengambil produk:', error);
        throw new Error(`Gagal mengambil daftar produk: ${error.message}`);
      }

      // Pastikan data yang dikembalikan sesuai dengan tipe Product
      const products: Product[] = data.map((item: any) => ({
        ...item,
        // Pastikan semua field yang diperlukan ada
        id: item.id || '',
        sku: item.sku || '',
        name: item.name || '',
        slug: item.slug || '',
        price: Number(item.price) || 0,
        track_inventory: Boolean(item.track_inventory),
        inventory_quantity: Number(item.inventory_quantity) || 0,
        low_stock_threshold: Number(item.low_stock_threshold) || 10,
        tags: Array.isArray(item.tags) ? item.tags : [],
        images: Array.isArray(item.images) ? item.images : [],
        gallery: Array.isArray(item.gallery) ? item.gallery : [],
        variants: Array.isArray(item.variants) ? item.variants : [],
        attributes: item.attributes && typeof item.attributes === 'object' ? item.attributes : {},
        seo_keywords: Array.isArray(item.seo_keywords) ? item.seo_keywords : [],
        status: ['draft', 'active', 'inactive', 'archived'].includes(item.status) 
          ? item.status as 'draft' | 'active' | 'inactive' | 'archived'
          : 'draft', // Default value if status is invalid
        visibility: ['visible', 'hidden', 'catalog_only'].includes(item.visibility)
          ? item.visibility as 'visible' | 'hidden' | 'catalog_only'
          : 'visible', // Default value if visibility is invalid
        featured: Boolean(item.featured),
        digital: Boolean(item.digital),
        downloadable: Boolean(item.downloadable),
        download_files: Array.isArray(item.download_files) ? item.download_files : [],
        requires_shipping: item.requires_shipping !== undefined ? Boolean(item.requires_shipping) : true,
        tax_status: ['taxable', 'none'].includes(item.tax_status)
          ? item.tax_status as 'taxable' | 'none'
          : 'taxable', // Default value if tax_status is invalid
        tax_class: item.tax_class || 'standard',
        meta_data: item.meta_data && typeof item.meta_data === 'object' ? item.meta_data : {},
        created_at: item.created_at || new Date().toISOString(),
        updated_at: item.updated_at || new Date().toISOString(),
        // Tambahkan relasi jika diperlukan
        category: item.category_id ? { id: item.category_id, name: '', slug: '' } : undefined,
        brand: item.brand_id ? { id: item.brand_id, name: '', slug: '' } : undefined
      }));

      console.log('Produk berhasil diambil:', { count: products.length });
      return products;
    } catch (error) {
      console.error('Error di getProducts:', error);
      throw error;
    }
  }

  // Get single product by ID
  async getProduct(id: string): Promise<Product | null> {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();

      if (error || !data) {
        console.error('Error fetching product:', error);
        return null;
      }

      // Dapatkan relasi terpisah
      const [category, brand] = await Promise.all([
        data.category_id ? this.getCategory(String(data.category_id)) : Promise.resolve(null),
        data.brand_id ? this.getBrand(String(data.brand_id)) : Promise.resolve(null)
      ]);

      return this.mapToProduct(data, category, brand);
    } catch (error) {
      console.error('Error in getProduct:', error);
      return null;
    }
  }

  // Get product by slug
  async getProductBySlug(slug: string): Promise<Product | null> {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .select('*')
        .eq('slug', slug)
        .single();

      if (error || !data) {
        console.error('Error fetching product by slug:', error);
        return null;
      }

      // Dapatkan relasi terpisah
      const [category, brand] = await Promise.all([
        data.category_id ? this.getCategory(String(data.category_id)) : Promise.resolve(null),
        data.brand_id ? this.getBrand(String(data.brand_id)) : Promise.resolve(null)
      ]);

      return this.mapToProduct(data, category, brand);
    } catch (error) {
      console.error('Error in getProductBySlug:', error);
      return null;
    }
  }

  // Helper method to get category
  private async getCategory(id: string | null | undefined): Promise<{ id: string; name: string; slug: string } | null> {
    if (!id) return null;
    try {
      const idString = String(id); // Konversi ke string untuk memastikan tipe data
      const { data, error } = await this.supabase
        .from('product_categories')
        .select('id, name, slug')
        .eq('id', idString)
        .single();

      if (error || !data) {
        console.error('Error fetching category:', error);
        return null;
      }

      // Pastikan tipe data sesuai
      return {
        id: String(data.id),
        name: String(data.name || ''),
        slug: String(data.slug || '')
      };
    } catch (error) {
      console.error('Error in getCategory:', error);
      return null;
    }
  }

  // Helper method to get brand
  private async getBrand(id: string | null | undefined): Promise<{ id: string; name: string; slug: string } | null> {
    if (!id) return null;
    try {
      const idString = String(id); // Konversi ke string untuk memastikan tipe data
      const { data, error } = await this.supabase
        .from('brands')
        .select('id, name, slug')
        .eq('id', idString)
        .single();

      if (error || !data) {
        console.error('Error fetching brand:', error);
        return null;
      }

      // Pastikan tipe data sesuai
      return {
        id: String(data.id),
        name: String(data.name || ''),
        slug: String(data.slug || '')
      };
    } catch (error) {
      console.error('Error in getBrand:', error);
      return null;
    }
  }

  /**
   * Creates or replaces the secure update function in the database
   * This function handles updates to products with proper permissions
   */
  private async createSecureUpdateFunction(): Promise<void> {
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION secure_update_product(
        p_id UUID,
        p_update_data JSONB
      )
      RETURNS JSONB
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      DECLARE
        result JSONB;
        column_name TEXT;
        column_value JSONB;
        query TEXT;
        where_conditions TEXT[] := ARRAY[]::TEXT[];
        set_clauses TEXT[] := ARRAY[]::TEXT[];
        allowed_columns TEXT[] := ARRAY[
          'sku', 'name', 'slug', 'description', 'short_description', 'price', 'compare_price',
          'cost_price', 'track_inventory', 'inventory_quantity', 'low_stock_threshold',
          'weight', 'dimensions', 'category_id', 'brand_id', 'tags', 'images', 'featured_image',
          'gallery', 'variants', 'attributes', 'seo_title', 'seo_description', 'seo_keywords',
          'status', 'visibility', 'featured', 'digital', 'downloadable', 'download_files',
          'requires_shipping', 'tax_status', 'tax_class', 'meta_data', 'updated_at', 'published_at'
        ];
      BEGIN
        -- Build the SET clauses from the JSONB object
        FOR column_name, column_value IN SELECT key, value FROM jsonb_each(p_update_data)
        LOOP
          -- Only include allowed columns to prevent SQL injection
          IF column_name = ANY(allowed_columns) THEN
            -- Handle NULL values properly
            IF column_value::text = 'null' THEN
              set_clauses := array_append(set_clauses, format('%I = NULL', column_name));
            ELSE
              set_clauses := array_append(set_clauses, format('%I = %L', column_name, column_value::text));
            END IF;
          END IF;
        END LOOP;

        -- If no valid columns to update, return error
        IF array_length(set_clauses, 1) IS NULL THEN
          RETURN jsonb_build_object('error', 'No valid columns to update');
        END IF;

        -- Build and execute the dynamic SQL
        query := format(
          'UPDATE products SET %s, updated_at = NOW() WHERE id = $1 RETURNING to_jsonb(products.*)',
          array_to_string(set_clauses, ', ')
        );

        EXECUTE query INTO result USING p_id;

        -- If no rows were updated, return error
        IF result IS NULL THEN
          RETURN jsonb_build_object('error', 'Product not found or no changes made');
        END IF;

        RETURN result;
      EXCEPTION WHEN OTHERS THEN
        RETURN jsonb_build_object('error', SQLERRM);
      END;
      $$;
    `;

    try {
      // Execute the SQL to create the function
      const { error } = await this.supabase.rpc('create_secure_update_function', {
        sql: createFunctionSQL
      });

      if (error) {
        console.error('Error creating secure update function:', error);
      }
    } catch (error) {
      console.error('Error in createSecureUpdateFunction:', error);
    }
  }

  // Helper method to generate slug from text
  private generateSlug(text: string): string {
    return text
      .toString()
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')           // Ganti spasi dengan -
      .replace(/[^\w\-]+/g, '')      // Hapus karakter non-word kecuali -
      .replace(/\-\-+/g, '-')        // Ganti multiple - dengan single -
      .replace(/^-+/, '')             // Hapus - dari awal teks
      .replace(/-+$/, '');            // Hapus - dari akhir teks
  }

  // Helper method to map database row to Product
  private mapToProduct(
    data: any,
    category: { id: string; name: string; slug: string } | null,
    brand: { id: string; name: string; slug: string } | null
  ): Product {
    return {
      id: data.id || '',
      sku: data.sku || '',
      name: data.name || '',
      slug: data.slug || '',
      description: data.description || '',
      short_description: data.short_description || '',
      price: Number(data.price) || 0,
      compare_price: data.compare_price ? Number(data.compare_price) : undefined,
      cost_price: data.cost_price ? Number(data.cost_price) : undefined,
      track_inventory: Boolean(data.track_inventory !== false), // Default true
      inventory_quantity: Number(data.inventory_quantity) || 0,
      low_stock_threshold: Number(data.low_stock_threshold) || 10,
      weight: data.weight ? Number(data.weight) : undefined,
      dimensions: data.dimensions && typeof data.dimensions === 'object' ? data.dimensions : undefined,
      category_id: data.category_id || undefined,
      brand_id: data.brand_id || undefined,
      tags: Array.isArray(data.tags) ? data.tags : [],
      images: Array.isArray(data.images) ? data.images : [],
      featured_image: data.featured_image || undefined,
      gallery: Array.isArray(data.gallery) ? data.gallery : [],
      variants: Array.isArray(data.variants) ? data.variants : [],
      attributes: data.attributes && typeof data.attributes === 'object' ? data.attributes : {},
      seo_title: data.seo_title || undefined,
      seo_description: data.seo_description || undefined,
      seo_keywords: Array.isArray(data.seo_keywords) ? data.seo_keywords : [],
      status: ['draft', 'active', 'inactive', 'archived'].includes(data.status) 
        ? data.status as 'draft' | 'active' | 'inactive' | 'archived'
        : 'draft',
      visibility: ['visible', 'hidden', 'catalog_only'].includes(data.visibility)
        ? data.visibility as 'visible' | 'hidden' | 'catalog_only'
        : 'visible',
      featured: Boolean(data.featured),
      digital: Boolean(data.digital),
      downloadable: Boolean(data.downloadable),
      download_files: Array.isArray(data.download_files) ? data.download_files : [],
      requires_shipping: data.requires_shipping !== false, // Default true
      tax_status: ['taxable', 'none'].includes(data.tax_status)
        ? data.tax_status as 'taxable' | 'none'
        : 'taxable',
      tax_class: data.tax_class || 'standard',
      meta_data: data.meta_data && typeof data.meta_data === 'object' ? data.meta_data : {},
      created_by: data.created_by || undefined,
      updated_by: data.updated_by || undefined,
      published_at: data.published_at || undefined,
      created_at: data.created_at || new Date().toISOString(),
      updated_at: data.updated_at || new Date().toISOString(),
      // Relations
      category: category || undefined,
      brand: brand || undefined
    };
  }

  // Create new product
  async createProduct(product: ProductCreate): Promise<Product> {
    try {
      console.log('Memulai proses pembuatan produk:', { product });
      
      // Validasi data produk
      if (!product.sku || !product.sku.trim()) {
        throw new Error('SKU produk tidak boleh kosong');
      }
      
      if (!product.name || !product.name.trim()) {
        throw new Error('Nama produk tidak boleh kosong');
      }
      
      if (!product.price || product.price <= 0) {
        throw new Error('Harga produk harus lebih dari 0');
      }

      // Check if SKU already exists
      console.log('Memeriksa keunikan SKU...');
      const existingProduct = await this.getProductBySku(product.sku);
      if (existingProduct) {
        throw new Error('SKU sudah digunakan oleh produk lain');
      }

      // Check if slug already exists
      console.log('Memeriksa keunikan slug...');
      if (product.slug) {
        const existingSlug = await this.getProductBySlug(product.slug);
        if (existingSlug) {
          throw new Error('Slug sudah digunakan oleh produk lain');
        }
      } else {
        // Generate slug dari nama jika tidak disediakan
        product.slug = this.generateSlug(product.name);
      }

      console.log('Menyiapkan data untuk disimpan...');
      const productData: any = {
        ...product,
        // Pastikan semua field memiliki nilai default yang sesuai
        name: product.name.trim(),
        slug: product.slug || this.generateSlug(product.name),
        description: product.description || '',
        short_description: product.short_description || '',
        price: Number(product.price) || 0,
        compare_price: product.compare_price ? Number(product.compare_price) : null,
        cost_price: product.cost_price ? Number(product.cost_price) : null,
        track_inventory: product.track_inventory !== undefined ? Boolean(product.track_inventory) : true,
        inventory_quantity: product.inventory_quantity ? Number(product.inventory_quantity) : 0,
        low_stock_threshold: product.low_stock_threshold ? Number(product.low_stock_threshold) : 10,
        weight: product.weight ? Number(product.weight) : null,
        dimensions: product.dimensions || {},
        category_id: product.category_id || null,
        brand_id: product.brand_id || null,
        tags: Array.isArray(product.tags) ? product.tags : [],
        images: Array.isArray(product.images) ? product.images : [],
        featured_image: product.featured_image || null,
        gallery: Array.isArray(product.gallery) ? product.gallery : [],
        variants: Array.isArray(product.variants) ? product.variants : [],
        attributes: product.attributes && typeof product.attributes === 'object' ? product.attributes : {},
        seo_title: product.seo_title || null,
        seo_description: product.seo_description || null,
        seo_keywords: Array.isArray(product.seo_keywords) ? product.seo_keywords : [],
        status: ['draft', 'active', 'inactive', 'archived'].includes(product.status || '')
          ? product.status
          : 'draft',
        visibility: ['visible', 'hidden', 'catalog_only'].includes(product.visibility || '')
          ? product.visibility
          : 'visible',
        featured: Boolean(product.featured),
        digital: Boolean(product.digital),
        downloadable: Boolean(product.downloadable),
        download_files: Array.isArray(product.download_files) ? product.download_files : [],
        requires_shipping: product.requires_shipping !== undefined ? Boolean(product.requires_shipping) : true,
        tax_status: ['taxable', 'none'].includes(product.tax_status || '')
          ? product.tax_status
          : 'taxable',
        tax_class: product.tax_class || 'standard',
        meta_data: product.meta_data && typeof product.meta_data === 'object' ? product.meta_data : {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('Mengirim data ke database...', { productData });
      
      // Simpan ke database
      const { data, error } = await this.supabase
        .from('products')
        .insert([productData])
        .select('*')
        .single();

      if (error) {
        console.error('Error saat menyimpan ke database:', error);
        throw new Error(`Gagal menyimpan produk: ${error.message}`);
      }

      console.log('Produk berhasil dibuat, mengambil data lengkap...');
      
      // Ambil data lengkap termasuk relasi
      const createdProduct = await this.getProduct(String(data.id));
      if (!createdProduct) {
        throw new Error('Gagal mengambil data produk yang baru dibuat');
      }

      console.log('Produk berhasil dibuat:', createdProduct);
      return createdProduct;
    } catch (error) {
      console.error('Error di createProduct:', error);
      throw error; // Re-throw error untuk ditangkap oleh route handler
    }
  }

  // Update product
  async updateProduct(id: string, updates: ProductUpdate): Promise<Product> {
    try {
      console.log('Memulai proses update produk:', { id, updates });
      
      // If SKU is being updated, check if it already exists
      if (updates.sku) {
        const existingProduct = await this.getProductBySku(updates.sku);
        if (existingProduct && existingProduct.id !== id) {
          throw new Error('SKU sudah digunakan oleh produk lain');
        }
      }

      // If slug is being updated, check if it already exists
      if (updates.slug) {
        const existingSlug = await this.getProductBySlug(updates.slug);
        if (existingSlug && existingSlug.id !== id) {
          throw new Error('Slug sudah digunakan oleh produk lain');
        }
      }

      // Create a copy of updates to avoid modifying the original
      const updateData: any = { ...updates };
      
      // Remove brand and category objects if they exist (we only want to use brand_id and category_id)
      if ('brand' in updateData) {
        delete updateData.brand;
      }
      if ('category' in updateData) {
        delete updateData.category;
      }
      
      // Add updated_at
      updateData.updated_at = new Date().toISOString();

      console.log('Mengupdate data produk:', updateData);
      
      // Create or update the secure update function
      await this.supabase.rpc('create_secure_update_function');
      
      // Update product data
      const { data, error } = await this.supabase.rpc('secure_update_product', {
        p_id: id,
        p_update_data: updateData
      });

      if (error) {
        console.error('Gagal mengupdate produk:', error);
        throw new Error(`Gagal mengupdate produk: ${error.message}`);
      }

      // Ambil data lengkap termasuk relasi
      const updatedProduct = await this.getProduct(id);
      if (!updatedProduct) {
        throw new Error('Gagal mengambil data produk yang baru diupdate');
      }

      console.log('Produk berhasil diupdate:', updatedProduct);
      return updatedProduct;
    } catch (error) {
      console.error('Error di updateProduct:', error);
      throw error;
    }
  }

  // Delete product
  async deleteProduct(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete product: ${error.message}`)
    }
  }

  // Get product by SKU
  async getProductBySku(sku: string): Promise<Product | null> {
    try {
      // Pastikan sku adalah string
      const skuString = String(sku);
      
      const { data, error } = await this.supabase
        .from('products')
        .select('*')
        .eq('sku', skuString)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        console.error('Error fetching product by SKU:', error);
        return null;
      }

      if (!data) return null;

      // Dapatkan relasi terpisah
      const [category, brand] = await Promise.all([
        data.category_id ? this.getCategory(String(data.category_id)) : Promise.resolve(null),
        data.brand_id ? this.getBrand(String(data.brand_id)) : Promise.resolve(null)
      ]);

      return this.mapToProduct(data, category, brand);
    } catch (error) {
      console.error('Error in getProductBySku:', error);
      return null;
    }
  }

  // Update product status
  async updateProductStatus(id: string, status: Product['status']): Promise<Product> {
    const updates: any = { status }
    
    if (status === 'active') {
      updates.published_at = new Date().toISOString()
    }

    return this.updateProduct(id, updates)
  }

  // Toggle featured status
  async toggleFeatured(id: string): Promise<Product> {
    const product = await this.getProduct(id)
    if (!product) {
      throw new Error('Product not found')
    }

    return this.updateProduct(id, { featured: !product.featured })
  }

  // Update inventory
  async updateInventory(id: string, quantity: number): Promise<Product> {
    return this.updateProduct(id, { inventory_quantity: quantity })
  }

  // Get product statistics
  async getProductStats(): Promise<{
    total: number
    active: number
    draft: number
    inactive: number
    featured: number
    low_stock: number
    out_of_stock: number
  }> {
    try {
      // Get total products
      const { count: total } = await this.supabase
        .from('products')
        .select('*', { count: 'exact', head: true })

      // Get active products
      const { count: active } = await this.supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active')

      // Get draft products
      const { count: draft } = await this.supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'draft')

      // Get inactive products
      const { count: inactive } = await this.supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'inactive')

      // Get featured products
      const { count: featured } = await this.supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('featured', true)

      // Get all products with inventory data
      const { data: products } = await this.supabase
        .from('products')
        .select('inventory_quantity, low_stock_threshold')
        .not('inventory_quantity', 'is', null)

      // Hitung low stock dan out of stock secara manual
      let low_stock = 0;
      let out_of_stock = 0;

      if (products) {
        products.forEach(product => {
          const quantity = Number(product.inventory_quantity) || 0;
          const threshold = Number(product.low_stock_threshold) || 0;

          if (quantity <= 0) {
            out_of_stock++;
          } else if (quantity <= threshold) {
            low_stock++;
          }
        });
      }

      return {
        total: total || 0,
        active: active || 0,
        draft: draft || 0,
        inactive: inactive || 0,
        featured: featured || 0,
        low_stock: low_stock,
        out_of_stock: out_of_stock
      };
    } catch (error) {
      console.error('Error getting product stats:', error);
      throw new Error('Gagal mengambil statistik produk');
    }
  }
}

export const productService = new ProductService();
