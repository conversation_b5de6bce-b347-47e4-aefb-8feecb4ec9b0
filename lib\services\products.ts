import { getClient } from '@/lib/supabase'

export interface Product {
  id: string
  sku: string
  name: string
  slug: string
  description?: string
  short_description?: string
  price: number
  compare_price?: number
  cost_price?: number
  track_inventory: boolean
  inventory_quantity: number
  low_stock_threshold: number
  weight?: number
  dimensions?: {
    length?: number
    width?: number
    height?: number
  }
  category_id?: string
  brand_id?: string
  tags: string[]
  images: string[]
  featured_image?: string
  gallery: string[]
  variants: any[]
  attributes: Record<string, any>
  seo_title?: string
  seo_description?: string
  seo_keywords: string[]
  status: 'draft' | 'active' | 'inactive' | 'archived'
  visibility: 'visible' | 'hidden' | 'catalog_only'
  featured: boolean
  digital: boolean
  downloadable: boolean
  download_files: string[]
  requires_shipping: boolean
  tax_status: 'taxable' | 'none'
  tax_class: string
  meta_data: Record<string, any>
  created_by?: string
  updated_by?: string
  published_at?: string
  created_at: string
  updated_at: string
  // Relations
  category?: {
    id: string
    name: string
    slug: string
  }
  brand?: {
    id: string
    name: string
    slug: string
  }
}

export interface ProductFilters {
  search?: string
  category_id?: string
  brand_id?: string
  status?: string
  visibility?: string
  featured?: boolean
  min_price?: number
  max_price?: number
  tags?: string[]
  in_stock?: boolean
}

export interface ProductCreate {
  sku: string
  name: string
  slug: string
  description?: string
  short_description?: string
  price: number
  compare_price?: number
  cost_price?: number
  track_inventory?: boolean
  inventory_quantity?: number
  low_stock_threshold?: number
  weight?: number
  dimensions?: object
  category_id?: string
  brand_id?: string
  tags?: string[]
  images?: string[]
  featured_image?: string
  gallery?: string[]
  variants?: any[]
  attributes?: object
  seo_title?: string
  seo_description?: string
  seo_keywords?: string[]
  status?: Product['status']
  visibility?: Product['visibility']
  featured?: boolean
  digital?: boolean
  downloadable?: boolean
  download_files?: string[]
  requires_shipping?: boolean
  tax_status?: Product['tax_status']
  tax_class?: string
  meta_data?: object
}

export interface ProductUpdate extends Partial<ProductCreate> {}

class ProductService {
  private supabase = getClient()

  // Get all products with optional filters and relations
  async getProducts(filters?: ProductFilters): Promise<Product[]> {
    let query = this.supabase
      .from('products')
      .select(`
        *,
        category:product_categories(id, name, slug),
        brand:brands(id, name, slug)
      `)
      .order('created_at', { ascending: false })

    // Apply filters
    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,sku.ilike.%${filters.search}%`)
    }

    if (filters?.category_id) {
      query = query.eq('category_id', filters.category_id)
    }

    if (filters?.brand_id) {
      query = query.eq('brand_id', filters.brand_id)
    }

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    if (filters?.visibility) {
      query = query.eq('visibility', filters.visibility)
    }

    if (filters?.featured !== undefined) {
      query = query.eq('featured', filters.featured)
    }

    if (filters?.min_price !== undefined) {
      query = query.gte('price', filters.min_price)
    }

    if (filters?.max_price !== undefined) {
      query = query.lte('price', filters.max_price)
    }

    if (filters?.in_stock) {
      query = query.gt('inventory_quantity', 0)
    }

    if (filters?.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch products: ${error.message}`)
    }

    return data || []
  }

  // Get single product by ID
  async getProduct(id: string): Promise<Product | null> {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        category:product_categories(id, name, slug),
        brand:brands(id, name, slug)
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch product: ${error.message}`)
    }

    return data
  }

  // Get product by slug
  async getProductBySlug(slug: string): Promise<Product | null> {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        category:product_categories(id, name, slug),
        brand:brands(id, name, slug)
      `)
      .eq('slug', slug)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch product: ${error.message}`)
    }

    return data
  }

  // Create new product
  async createProduct(product: ProductCreate): Promise<Product> {
    // Check if SKU already exists
    const existingProduct = await this.getProductBySku(product.sku)
    if (existingProduct) {
      throw new Error('SKU already exists')
    }

    // Check if slug already exists
    const existingSlug = await this.getProductBySlug(product.slug)
    if (existingSlug) {
      throw new Error('Slug already exists')
    }

    const { data, error } = await this.supabase
      .from('products')
      .insert([{
        ...product,
        track_inventory: product.track_inventory !== undefined ? product.track_inventory : true,
        inventory_quantity: product.inventory_quantity || 0,
        low_stock_threshold: product.low_stock_threshold || 10,
        tags: product.tags || [],
        images: product.images || [],
        gallery: product.gallery || [],
        variants: product.variants || [],
        attributes: product.attributes || {},
        seo_keywords: product.seo_keywords || [],
        status: product.status || 'draft',
        visibility: product.visibility || 'visible',
        featured: product.featured || false,
        digital: product.digital || false,
        downloadable: product.downloadable || false,
        download_files: product.download_files || [],
        requires_shipping: product.requires_shipping !== undefined ? product.requires_shipping : true,
        tax_status: product.tax_status || 'taxable',
        tax_class: product.tax_class || 'standard',
        meta_data: product.meta_data || {}
      }])
      .select(`
        *,
        category:product_categories(id, name, slug),
        brand:brands(id, name, slug)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to create product: ${error.message}`)
    }

    return data
  }

  // Update product
  async updateProduct(id: string, updates: ProductUpdate): Promise<Product> {
    // If SKU is being updated, check if it already exists
    if (updates.sku) {
      const existingProduct = await this.getProductBySku(updates.sku)
      if (existingProduct && existingProduct.id !== id) {
        throw new Error('SKU already exists')
      }
    }

    // If slug is being updated, check if it already exists
    if (updates.slug) {
      const existingSlug = await this.getProductBySlug(updates.slug)
      if (existingSlug && existingSlug.id !== id) {
        throw new Error('Slug already exists')
      }
    }

    const { data, error } = await this.supabase
      .from('products')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        category:product_categories(id, name, slug),
        brand:brands(id, name, slug)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update product: ${error.message}`)
    }

    return data
  }

  // Delete product
  async deleteProduct(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete product: ${error.message}`)
    }
  }

  // Get product by SKU
  async getProductBySku(sku: string): Promise<Product | null> {
    const { data, error } = await this.supabase
      .from('products')
      .select('*')
      .eq('sku', sku)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      throw new Error(`Failed to fetch product: ${error.message}`)
    }

    return data
  }

  // Update product status
  async updateProductStatus(id: string, status: Product['status']): Promise<Product> {
    const updates: any = { status }
    
    if (status === 'active') {
      updates.published_at = new Date().toISOString()
    }

    return this.updateProduct(id, updates)
  }

  // Toggle featured status
  async toggleFeatured(id: string): Promise<Product> {
    const product = await this.getProduct(id)
    if (!product) {
      throw new Error('Product not found')
    }

    return this.updateProduct(id, { featured: !product.featured })
  }

  // Update inventory
  async updateInventory(id: string, quantity: number): Promise<Product> {
    return this.updateProduct(id, { inventory_quantity: quantity })
  }

  // Get product statistics
  async getProductStats(): Promise<{
    total: number
    active: number
    draft: number
    inactive: number
    featured: number
    low_stock: number
    out_of_stock: number
  }> {
    const { data, error } = await this.supabase
      .from('products')
      .select('status, featured, inventory_quantity, low_stock_threshold')

    if (error) {
      throw new Error(`Failed to fetch product stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      active: 0,
      draft: 0,
      inactive: 0,
      featured: 0,
      low_stock: 0,
      out_of_stock: 0,
    }

    data.forEach((product) => {
      // Status counts
      if (product.status === 'active') stats.active++
      else if (product.status === 'draft') stats.draft++
      else if (product.status === 'inactive') stats.inactive++

      // Featured count
      if (product.featured) stats.featured++

      // Stock counts
      if (product.inventory_quantity === 0) {
        stats.out_of_stock++
      } else if (product.inventory_quantity <= product.low_stock_threshold) {
        stats.low_stock++
      }
    })

    return stats
  }
}

export const productService = new ProductService()
