import { NextRequest, NextResponse } from 'next/server';
import { productCategoryService } from '@/lib/services/product-categories';

// GET - Mendapatkan product category berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    const category = await productCategoryService.getCategory(id);
    
    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(category);
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json(
      { error: 'Failed to fetch category' },
      { status: 500 }
    );
  }
}

// PUT - Update product category berdasarkan ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    const updatedCategory = await productCategoryService.updateCategory(id, body);
    
    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error('Error updating category:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update category';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// DELETE - Hapus product category berdasarkan ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    await productCategoryService.deleteCategory(id);
    
    return NextResponse.json({ 
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// PATCH - Toggle status atau operasi khusus lainnya
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { action } = body;
    
    let updatedCategory;
    
    switch (action) {
      case 'toggle_status':
        updatedCategory = await productCategoryService.toggleCategoryStatus(id);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: toggle_status' },
          { status: 400 }
        );
    }
    
    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error('Error updating category status:', error);
    return NextResponse.json(
      { error: 'Failed to update category status' },
      { status: 500 }
    );
  }
}
